# 多元线性回归优化算法比较实验

本项目实现了六种不同的优化算法来求解256元线性回归问题，并比较它们的收敛性能。

## 🚀 快速开始

运行主程序，选择比较模式：

```bash
python3 main.py
```

### 运行模式选择

程序提供四种运行模式：

1. **六种算法全面比较** - 比较所有六种优化算法的性能
2. **三种算法优化效果对比** - 比较原版和优化版算法的性能提升
3. **运行所有比较** - 依次运行上述两种比较
4. **保存最优权重和损失函数值** - 输出所有9种算法的最优权重向量和对应损失函数值

### 优化版算法

项目还包含三个优化版算法：
- `script/gradient_descent_optimized.py` - 优化版梯度下降法
- `script/newton_optimized.py` - 优化版牛顿法
- `script/quasi_newton_optimized.py` - 优化版BFGS法

## 📊 实验结果亮点

### 六种算法性能比较表

| 算法 | 迭代次数 | 耗时(秒) | R² | 收敛性 |
|------|----------|----------|-----|--------|
| 梯度下降法 | 696 | 0.1446 | 1.0000 | ✅ 良好 |
| 牛顿法 | 3 | 0.0169 | 1.0000 | ✅ 优秀 |
| BFGS法 | 18 | 0.0330 | 1.0000 | ✅ 优秀 |
| 共轭梯度法 | 18 | 0.0082 | 1.0000 | ✅ 优秀 |
| Adagrad优化器 | 1000 | 0.2686 | -650.57 | ❌ 发散 |
| L-BFGS法 | 23 | 0.0061 | 1.0000 | ✅ 优秀 |

### 优化效果对比表

| 算法 | 版本 | 迭代次数 | 耗时(秒) | R² | 优化效果 |
|------|------|----------|----------|-----|----------|
| 梯度下降法 | 原版 | 696 | 0.270 | 1.0000 | - |
| 梯度下降法 | 优化版 | 608 | 0.312 | 1.0000 | ✅ 迭代减少12.6% |
| 牛顿法 | 原版 | 3 | 0.031 | 1.0000 | - |
| 牛顿法 | 优化版 | 1 | 0.003 | 1.0000 | ⭐ 时间提升10倍 |
| BFGS法 | 原版 | 18 | 0.074 | 1.0000 | - |
| BFGS法 | 优化版 | 1 | 0.018 | 1.0000 | ⭐ 时间提升4倍 |

### 🏆 关键发现

#### 收敛速度排名（按迭代次数）
1. **牛顿法** - 3次迭代（最快）
2. **BFGS法** - 18次迭代
3. **共轭梯度法** - 18次迭代
4. **L-BFGS法** - 23次迭代
5. **梯度下降法** - 696次迭代
6. **Adagrad优化器** - 1000次迭代（未收敛）

#### 计算效率排名（按耗时）
1. **L-BFGS法** - 0.0061秒（最快）⭐
2. **共轭梯度法** - 0.0082秒⭐
3. **牛顿法** - 0.0169秒
4. **BFGS法** - 0.0330秒
5. **梯度下降法** - 0.1446秒
6. **Adagrad优化器** - 0.2686秒

## 🔬 算法详解

### 原有算法（前三种）

#### 1. 梯度下降法 (Gradient Descent)
- **文件**: `script/gradient_descent.py`
- **特点**: 最基础的优化算法，沿着负梯度方向更新参数
- **优点**: 简单易实现，内存需求低
- **缺点**: 收敛速度较慢，可能陷入局部最优

#### 2. 牛顿法 (Newton Method)
- **文件**: `script/newton.py`
- **特点**: 使用二阶导数信息（海森矩阵）进行优化
- **优点**: 收敛速度快，二次收敛
- **缺点**: 计算海森矩阵逆矩阵开销大，内存需求高

#### 3. BFGS法 (Quasi-Newton Method)
- **文件**: `script/quasi_newton.py`
- **特点**: 类牛顿法，近似计算海森矩阵逆
- **优点**: 比牛顿法内存需求低，比梯度下降收敛快
- **缺点**: 仍需存储完整的近似海森矩阵

### 新增算法（后三种）

#### 4. 共轭梯度法 (Conjugate Gradient)
- **文件**: `script/conjugate_gradient.py`
- **特点**: 利用共轭方向进行搜索，避免重复搜索
- **优点**: 收敛速度快，内存需求低，适合大规模问题
- **缺点**: 对病态问题敏感
- **实验表现**: ⭐ 效率最高（0.0082秒）

#### 5. Adagrad优化器 (Adagrad Optimizer)
- **文件**: `script/adam.py`
- **特点**: 自适应学习率，累积历史梯度信息调整学习率
- **优点**: 自动调整学习率，对稀疏数据效果好
- **缺点**: 学习率会逐渐衰减到零，可能过早停止学习
- **实验表现**: ❌ 在此问题上发散

#### 6. L-BFGS法 (Limited-memory BFGS)
- **文件**: `script/lbfgs.py`
- **特点**: BFGS的内存优化版本，只存储有限的历史信息
- **优点**: 内存需求低，收敛速度快，适合大规模问题
- **缺点**: 实现复杂度较高
- **实验表现**: ⭐ 综合性能最佳

## 📈 算法特性比较

| 算法 | 收敛速度 | 内存需求 | 计算复杂度 | 适用场景 |
|------|----------|----------|------------|----------|
| 梯度下降法 | 慢 | 低 | 低 | 简单问题，教学演示 |
| 牛顿法 | 很快 | 高 | 高 | 小规模问题，高精度要求 |
| BFGS法 | 快 | 中等 | 中等 | 中等规模问题 |
| 共轭梯度法 | 快 | 低 | 中等 | 大规模稀疏问题 |
| Adagrad优化器 | 中等 | 低 | 低 | 稀疏数据，在线学习 |
| L-BFGS法 | 很快 | 低 | 中等 | 大规模问题，高精度要求 |

## 🎯 算法选择建议

- **小规模问题（< 1000维）**: 推荐牛顿法或BFGS法
- **大规模问题（> 10000维）**: 推荐L-BFGS法或共轭梯度法
- **稀疏数据应用**: 推荐Adagrad优化器
- **教学演示**: 推荐梯度下降法
- **高精度要求**: 推荐牛顿法或L-BFGS法
- **内存受限**: 推荐共轭梯度法或Adagrad优化器

## 📁 项目结构

```
teamwork/
├── main.py                    # 主程序，运行所有算法比较
├── README.md                  # 项目说明文档
├── data/                      # 数据文件目录
│   └── group_dataset.csv      # 256元线性回归数据集
├── script/                    # 算法实现目录
│   ├── gradient_descent.py    # 梯度下降法
│   ├── newton.py             # 牛顿法
│   ├── quasi_newton.py       # BFGS法
│   ├── conjugate_gradient.py # 共轭梯度法
│   ├── adam.py               # Adagrad优化器
│   └── lbfgs.py              # L-BFGS法
└── output/                    # 结果输出目录
    ├── algorithm_results.csv      # 详细数值结果
    ├── individual_convergence.jpg # 各算法收敛曲线
    ├── convergence_comparison.jpg # 算法收敛比较
    ├── algorithm_comparison.jpg   # 性能指标比较
    ├── optimization_comparison.csv # 优化版vs原版算法比较
    ├── optimization_comparison.jpg # 优化效果对比图
    └── optimal_weights_and_loss_transposed.csv # 9种算法的最优权重和损失函数值
```

## 🔍 实验分析

### 优秀表现的算法

#### 1. L-BFGS法（最佳综合性能）
- **优点**: 计算时间最短，收敛快，精度高
- **适用**: 大规模线性回归问题的首选

#### 2. 共轭梯度法（最佳效率）
- **优点**: 计算时间短，收敛快，内存需求低
- **适用**: 大规模稀疏问题

#### 3. 牛顿法（最快收敛）
- **优点**: 收敛速度最快（仅3次迭代）
- **缺点**: 需要计算海森矩阵，内存开销大

### 问题分析

#### Adagrad优化器发散原因
1. **学习率衰减过快**: 导致参数更新幅度过小
2. **算法特性**: 对于凸优化问题，传统方法更适合
3. **适用场景**: Adagrad更适合稀疏数据和非凸优化

## 📊 评价指标

1. **迭代次数**: 算法达到收敛所需的迭代次数
2. **计算时间**: 算法运行的总时间
3. **R²值**: 模型拟合优度，越接近1越好
4. **收敛曲线**: 损失函数随迭代次数的变化

## 🚀 运行说明

### 环境要求
- Python 3.x
- NumPy
- Pandas
- Matplotlib
- Scikit-learn

### 安装依赖
```bash
pip install numpy pandas matplotlib scikit-learn
```

### 运行实验
```bash
python3 main.py
```

### 输出文件
程序运行后会在 `output/` 目录生成：

**六种算法比较模式：**
- `algorithm_results.csv` - 六种算法详细结果
- `individual_convergence.jpg` - 各算法收敛曲线
- `convergence_comparison.jpg` - 算法收敛比较
- `algorithm_comparison.jpg` - 性能指标比较

**优化效果对比模式：**
- `optimization_comparison.csv` - 原版vs优化版结果
- `optimization_comparison.jpg` - 优化效果对比图

**最优权重和损失函数值模式：**
- `optimal_weights_and_loss_transposed.csv` - 包含9种算法的最优权重向量和对应损失函数值

## 🎓 实验启示

1. **二阶方法优势明显**: 牛顿法、BFGS法、L-BFGS法都表现优秀
2. **共轭梯度法性价比高**: 在保持高精度的同时计算效率最高
3. **自适应方法需谨慎**: Adagrad在凸优化问题上可能不如传统方法
4. **线性回归特殊性**: 作为凸优化问题，传统优化方法表现更稳定

## 🔮 改进方向

1. 为Adagrad添加学习率调度策略
2. 实现更多自适应算法（如Adam、RMSprop）
3. 添加正则化项测试算法鲁棒性
4. 在不同规模数据集上测试算法扩展性

---

**实验数据**: 256维特征，1599个训练样本，400个测试样本
**最佳算法**: L-BFGS法（综合性能）、共轭梯度法（计算效率）
**实验结论**: 对于多元线性回归问题，二阶优化方法和共轭梯度法表现最优