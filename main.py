import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import matplotlib.font_manager as fm

# 配置中文字体支持
# 查找PingFang SC字体
font_path = '/System/Library/Fonts/PingFang.ttc'  # macOS系统上PingFang字体的路径
if os.path.exists(font_path):
    # 创建字体属性对象
    font_prop = fm.FontProperties(fname=font_path, size=12)
    # 设置全局字体
    plt.rcParams['font.family'] = ['sans-serif']
    # 将中文字体设置为默认字体
    plt.rcParams['font.sans-serif'] = ['PingFang SC'] + plt.rcParams['font.sans-serif']
else:
    # 如果找不到PingFang字体，尝试使用其他可能的中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Heiti TC', 'Microsoft YaHei', 'STHeiti', 'Arial Unicode MS'] 

# 解决负号显示问题
plt.rcParams['axes.unicode_minus'] = False

# 导入自定义算法
from script.gradient_descent import fit_and_predict as gd_fit
from script.newton import fit_and_predict as newton_fit
from script.quasi_newton import fit_and_predict as bfgs_fit
from script.conjugate_gradient import fit_and_predict as cg_fit
from script.adam import fit_and_predict as adam_fit
from script.lbfgs import fit_and_predict as lbfgs_fit

# 导入优化版算法
from script.gradient_descent_optimized import fit_and_predict as gd_optimized_fit
from script.newton_optimized import fit_and_predict as newton_optimized_fit
from script.quasi_newton_optimized import fit_and_predict as bfgs_optimized_fit

# 创建输出目录（如果不存在）
os.makedirs("output", exist_ok=True)

# 加载数据集
def load_data(file_path='data/group_dataset.csv'):
    """
    加载数据集并预处理
    """
    df = pd.read_csv(file_path, header=0)
    
    # 从第2行开始有效数据，前256列是特征，最后一列是目标变量
    X = df.iloc[1:, 1:257].values.astype(float)  # 特征矩阵
    y = df.iloc[1:, 257].values.astype(float)    # 目标变量
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42)
    
    # 标准化特征
    scaler = StandardScaler()
    X_train = scaler.fit_transform(X_train)
    X_test = scaler.transform(X_test)
    
    return X_train, X_test, y_train, y_test

# 运行和比较不同算法
def run_and_compare():
    """
    运行六种优化算法并比较结果
    """
    print("正在加载数据...")
    X_train, X_test, y_train, y_test = load_data()
    print(f"数据加载完成。训练集形状: {X_train.shape}, 测试集形状: {X_test.shape}")

    # 算法参数
    max_iterations = 1000
    tol = 1e-6

    # 运行梯度下降法
    print("\n正在运行梯度下降法...")
    _, _, gd_losses, gd_iterations, gd_time, gd_r2, _ = gd_fit(
        X_train, y_train, X_test, learning_rate=0.01, max_iterations=max_iterations, tol=tol
    )
    print(f"梯度下降法完成，迭代次数: {gd_iterations}, 耗时: {gd_time:.4f}秒, R²: {gd_r2:.4f}")

    # 运行牛顿法
    print("\n正在运行牛顿法...")
    _, _, newton_losses, newton_iterations, newton_time, newton_r2, _ = newton_fit(
        X_train, y_train, X_test, max_iterations=max_iterations, tol=tol
    )
    print(f"牛顿法完成，迭代次数: {newton_iterations}, 耗时: {newton_time:.4f}秒, R²: {newton_r2:.4f}")

    # 运行BFGS法（类牛顿法）
    print("\n正在运行BFGS法（类牛顿法）...")
    _, _, bfgs_losses, bfgs_iterations, bfgs_time, bfgs_r2, _ = bfgs_fit(
        X_train, y_train, X_test, max_iterations=max_iterations, tol=tol
    )
    print(f"BFGS法完成，迭代次数: {bfgs_iterations}, 耗时: {bfgs_time:.4f}秒, R²: {bfgs_r2:.4f}")

    # 运行共轭梯度法
    print("\n正在运行共轭梯度法...")
    _, _, cg_losses, cg_iterations, cg_time, cg_r2, _ = cg_fit(
        X_train, y_train, X_test, max_iterations=max_iterations, tol=tol
    )
    print(f"共轭梯度法完成，迭代次数: {cg_iterations}, 耗时: {cg_time:.4f}秒, R²: {cg_r2:.4f}")

    # 运行Adagrad优化器
    print("\n正在运行Adagrad优化器...")
    _, _, adam_losses, adam_iterations, adam_time, adam_r2, _ = adam_fit(
        X_train, y_train, X_test, learning_rate=0.1, max_iterations=max_iterations, tol=tol
    )
    print(f"Adagrad优化器完成，迭代次数: {adam_iterations}, 耗时: {adam_time:.4f}秒, R²: {adam_r2:.4f}")

    # 运行L-BFGS法
    print("\n正在运行L-BFGS法...")
    _, _, lbfgs_losses, lbfgs_iterations, lbfgs_time, lbfgs_r2, _ = lbfgs_fit(
        X_train, y_train, X_test, max_iterations=max_iterations, tol=tol
    )
    print(f"L-BFGS法完成，迭代次数: {lbfgs_iterations}, 耗时: {lbfgs_time:.4f}秒, R²: {lbfgs_r2:.4f}")

    # 比较结果
    results = {
        "算法": ["梯度下降法", "牛顿法", "BFGS法", "共轭梯度法", "Adagrad优化器", "L-BFGS法"],
        "迭代次数": [gd_iterations, newton_iterations, bfgs_iterations, cg_iterations, adam_iterations, lbfgs_iterations],
        "耗时(秒)": [gd_time, newton_time, bfgs_time, cg_time, adam_time, lbfgs_time],
        "R²": [gd_r2, newton_r2, bfgs_r2, cg_r2, adam_r2, lbfgs_r2]
    }
    
    results_df = pd.DataFrame(results)
    print("\n算法比较结果:")
    print(results_df)
    
    # 获取字体属性对象，用于图像标题和标签
    if 'font_prop' in globals():
        font_prop_title = fm.FontProperties(fname=font_path, size=14)
        font_prop_label = fm.FontProperties(fname=font_path, size=12)
    else:
        font_prop_title = None
        font_prop_label = None
    
    # 绘制损失函数收敛图
    plt.figure(figsize=(18, 12))

    # 绘制各个算法的收敛曲线
    plt.subplot(2, 3, 1)
    plt.semilogy(range(len(gd_losses)), gd_losses, 'b-', linewidth=2)
    plt.title('梯度下降法 - 损失函数收敛曲线', fontproperties=font_prop_title)
    plt.xlabel('迭代次数', fontproperties=font_prop_label)
    plt.ylabel('损失函数值 (对数刻度)', fontproperties=font_prop_label)
    plt.grid(True)

    plt.subplot(2, 3, 2)
    plt.semilogy(range(len(newton_losses)), newton_losses, 'r-', linewidth=2)
    plt.title('牛顿法 - 损失函数收敛曲线', fontproperties=font_prop_title)
    plt.xlabel('迭代次数', fontproperties=font_prop_label)
    plt.ylabel('损失函数值 (对数刻度)', fontproperties=font_prop_label)
    plt.grid(True)

    plt.subplot(2, 3, 3)
    plt.semilogy(range(len(bfgs_losses)), bfgs_losses, 'g-', linewidth=2)
    plt.title('BFGS法 - 损失函数收敛曲线', fontproperties=font_prop_title)
    plt.xlabel('迭代次数', fontproperties=font_prop_label)
    plt.ylabel('损失函数值 (对数刻度)', fontproperties=font_prop_label)
    plt.grid(True)

    plt.subplot(2, 3, 4)
    plt.semilogy(range(len(cg_losses)), cg_losses, 'm-', linewidth=2)
    plt.title('共轭梯度法 - 损失函数收敛曲线', fontproperties=font_prop_title)
    plt.xlabel('迭代次数', fontproperties=font_prop_label)
    plt.ylabel('损失函数值 (对数刻度)', fontproperties=font_prop_label)
    plt.grid(True)

    plt.subplot(2, 3, 5)
    plt.semilogy(range(len(adam_losses)), adam_losses, 'c-', linewidth=2)
    plt.title('Adagrad优化器 - 损失函数收敛曲线', fontproperties=font_prop_title)
    plt.xlabel('迭代次数', fontproperties=font_prop_label)
    plt.ylabel('损失函数值 (对数刻度)', fontproperties=font_prop_label)
    plt.grid(True)

    plt.subplot(2, 3, 6)
    plt.semilogy(range(len(lbfgs_losses)), lbfgs_losses, 'orange', linewidth=2)
    plt.title('L-BFGS法 - 损失函数收敛曲线', fontproperties=font_prop_title)
    plt.xlabel('迭代次数', fontproperties=font_prop_label)
    plt.ylabel('损失函数值 (对数刻度)', fontproperties=font_prop_label)
    plt.grid(True)

    plt.tight_layout()
    plt.savefig('output/individual_convergence.jpg', dpi=300)
    print("各算法收敛图已保存到 'output/individual_convergence.jpg'")

    # 在同一图上比较所有算法
    plt.figure(figsize=(12, 8))
    min_iterations = min(len(gd_losses), len(newton_losses), len(bfgs_losses),
                        len(cg_losses), len(adam_losses), len(lbfgs_losses))

    plt.semilogy(range(min(len(gd_losses), min_iterations)), gd_losses[:min_iterations], 'b-', linewidth=2, label='梯度下降法')
    plt.semilogy(range(min(len(newton_losses), min_iterations)), newton_losses[:min_iterations], 'r-', linewidth=2, label='牛顿法')
    plt.semilogy(range(min(len(bfgs_losses), min_iterations)), bfgs_losses[:min_iterations], 'g-', linewidth=2, label='BFGS法')
    plt.semilogy(range(min(len(cg_losses), min_iterations)), cg_losses[:min_iterations], 'm-', linewidth=2, label='共轭梯度法')
    plt.semilogy(range(min(len(adam_losses), min_iterations)), adam_losses[:min_iterations], 'c-', linewidth=2, label='Adagrad优化器')
    plt.semilogy(range(min(len(lbfgs_losses), min_iterations)), lbfgs_losses[:min_iterations], 'orange', linewidth=2, label='L-BFGS法')

    plt.title('六种算法损失函数收敛比较', fontproperties=font_prop_title)
    plt.xlabel('迭代次数', fontproperties=font_prop_label)
    plt.ylabel('损失函数值 (对数刻度)', fontproperties=font_prop_label)
    plt.legend(prop=font_prop_label)
    plt.grid(True)

    plt.tight_layout()
    plt.savefig('output/convergence_comparison.jpg', dpi=300)
    print("收敛比较图已保存到 'output/convergence_comparison.jpg'")
    
    # 绘制迭代次数、时间和精度比较柱状图
    fig, axs = plt.subplots(1, 3, figsize=(20, 8))

    algorithms = results["算法"]
    x = np.arange(len(algorithms))  # x轴位置
    colors = ['blue', 'red', 'green', 'magenta', 'cyan', 'orange']

    # 迭代次数比较
    axs[0].bar(x, results["迭代次数"], color=colors)
    axs[0].set_title('迭代次数比较', fontproperties=font_prop_title)
    axs[0].set_ylabel('迭代次数', fontproperties=font_prop_label)
    axs[0].grid(axis='y', linestyle='--', alpha=0.7)
    # 正确设置x轴刻度和标签
    axs[0].set_xticks(x)
    axs[0].set_xticklabels(algorithms, fontproperties=font_prop_label, rotation=45, ha='right')

    # 时间比较
    axs[1].bar(x, results["耗时(秒)"], color=colors)
    axs[1].set_title('计算时间比较', fontproperties=font_prop_title)
    axs[1].set_ylabel('时间 (秒)', fontproperties=font_prop_label)
    axs[1].grid(axis='y', linestyle='--', alpha=0.7)
    axs[1].set_xticks(x)
    axs[1].set_xticklabels(algorithms, fontproperties=font_prop_label, rotation=45, ha='right')

    # R²比较
    axs[2].bar(x, results["R²"], color=colors)
    axs[2].set_title('R²比较', fontproperties=font_prop_title)
    axs[2].set_ylabel('R²值', fontproperties=font_prop_label)
    axs[2].grid(axis='y', linestyle='--', alpha=0.7)
    axs[2].set_xticks(x)
    axs[2].set_xticklabels(algorithms, fontproperties=font_prop_label, rotation=45, ha='right')
    
    plt.tight_layout()
    plt.savefig('output/algorithm_comparison.jpg', dpi=300)
    print("算法比较图已保存到 'output/algorithm_comparison.jpg'")
    
    # 保存结果到CSV
    results_df.to_csv('output/algorithm_results.csv', index=False)
    print("结果数据已保存到 'output/algorithm_results.csv'")

# 优化算法对比模块
def run_optimization_comparison():
    """
    运行原版和优化版算法对比
    """
    print("正在加载数据...")
    X_train, X_test, y_train, y_test = load_data()
    print(f"数据加载完成。训练集形状: {X_train.shape}, 测试集形状: {X_test.shape}")

    # 算法参数
    max_iterations = 1000
    tol = 1e-6

    results = []

    print("\n=== 梯度下降法比较 ===")
    # 原版梯度下降法
    print("运行原版梯度下降法...")
    _, _, gd_orig_losses, gd_orig_iter, gd_orig_time, gd_orig_r2, _ = gd_fit(
        X_train, y_train, X_test, learning_rate=0.01, max_iterations=max_iterations, tol=tol
    )
    print(f"原版: 迭代{gd_orig_iter}次, 耗时{gd_orig_time:.4f}秒, R²={gd_orig_r2:.6f}")

    # 优化版梯度下降法
    print("运行优化版梯度下降法...")
    _, _, gd_opt_losses, gd_opt_iter, gd_opt_time, gd_opt_r2, _ = gd_optimized_fit(
        X_train, y_train, X_test, learning_rate=0.01, max_iterations=max_iterations, tol=tol
    )
    print(f"优化版: 迭代{gd_opt_iter}次, 耗时{gd_opt_time:.4f}秒, R²={gd_opt_r2:.6f}")

    results.extend([
        ["梯度下降法(原版)", gd_orig_iter, gd_orig_time, gd_orig_r2],
        ["梯度下降法(优化版)", gd_opt_iter, gd_opt_time, gd_opt_r2]
    ])

    print("\n=== 牛顿法比较 ===")
    # 原版牛顿法
    print("运行原版牛顿法...")
    _, _, newton_orig_losses, newton_orig_iter, newton_orig_time, newton_orig_r2, _ = newton_fit(
        X_train, y_train, X_test, max_iterations=max_iterations, tol=tol
    )
    print(f"原版: 迭代{newton_orig_iter}次, 耗时{newton_orig_time:.4f}秒, R²={newton_orig_r2:.6f}")

    # 优化版牛顿法
    print("运行优化版牛顿法...")
    _, _, newton_opt_losses, newton_opt_iter, newton_opt_time, newton_opt_r2, _ = newton_optimized_fit(
        X_train, y_train, X_test, max_iterations=max_iterations, tol=tol
    )
    print(f"优化版: 迭代{newton_opt_iter}次, 耗时{newton_opt_time:.4f}秒, R²={newton_opt_r2:.6f}")

    results.extend([
        ["牛顿法(原版)", newton_orig_iter, newton_orig_time, newton_orig_r2],
        ["牛顿法(优化版)", newton_opt_iter, newton_opt_time, newton_opt_r2]
    ])

    print("\n=== BFGS法比较 ===")
    # 原版BFGS法
    print("运行原版BFGS法...")
    _, _, bfgs_orig_losses, bfgs_orig_iter, bfgs_orig_time, bfgs_orig_r2, _ = bfgs_fit(
        X_train, y_train, X_test, max_iterations=max_iterations, tol=tol
    )
    print(f"原版: 迭代{bfgs_orig_iter}次, 耗时{bfgs_orig_time:.4f}秒, R²={bfgs_orig_r2:.6f}")

    # 优化版BFGS法
    print("运行优化版BFGS法...")
    _, _, bfgs_opt_losses, bfgs_opt_iter, bfgs_opt_time, bfgs_opt_r2, _ = bfgs_optimized_fit(
        X_train, y_train, X_test, max_iterations=max_iterations, tol=tol
    )
    print(f"优化版: 迭代{bfgs_opt_iter}次, 耗时{bfgs_opt_time:.4f}秒, R²={bfgs_opt_r2:.6f}")

    results.extend([
        ["BFGS法(原版)", bfgs_orig_iter, bfgs_orig_time, bfgs_orig_r2],
        ["BFGS法(优化版)", bfgs_opt_iter, bfgs_opt_time, bfgs_opt_r2]
    ])

    # 创建结果DataFrame
    results_df = pd.DataFrame(results, columns=["算法", "迭代次数", "耗时(秒)", "R²"])
    print("\n=== 优化效果总结 ===")
    print(results_df)

    # 保存结果
    results_df.to_csv('output/optimization_comparison.csv', index=False)
    print("\n结果已保存到 'output/optimization_comparison.csv'")

    # 绘制优化对比图
    plot_optimization_comparison(results_df)

    return results_df

def plot_optimization_comparison(results_df):
    """绘制优化对比图表"""
    # 获取字体属性
    if 'font_prop' in globals():
        font_prop_title = fm.FontProperties(fname=font_path, size=14)
        font_prop_label = fm.FontProperties(fname=font_path, size=12)
    else:
        font_prop_title = None
        font_prop_label = None

    # 创建比较图
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))

    # 分组数据
    algorithms = ['梯度下降法', '牛顿法', 'BFGS法']
    original_data = results_df[results_df['算法'].str.contains('原版')]
    optimized_data = results_df[results_df['算法'].str.contains('优化版')]

    x = np.arange(len(algorithms))
    width = 0.35

    # 迭代次数比较
    axes[0].bar(x - width/2, original_data['迭代次数'], width, label='原版', color='lightblue')
    axes[0].bar(x + width/2, optimized_data['迭代次数'], width, label='优化版', color='orange')
    axes[0].set_title('迭代次数比较', fontproperties=font_prop_title)
    axes[0].set_ylabel('迭代次数', fontproperties=font_prop_label)
    axes[0].set_xticks(x)
    axes[0].set_xticklabels(algorithms, fontproperties=font_prop_label)
    axes[0].legend(prop=font_prop_label)
    axes[0].grid(axis='y', alpha=0.3)

    # 计算时间比较
    axes[1].bar(x - width/2, original_data['耗时(秒)'], width, label='原版', color='lightblue')
    axes[1].bar(x + width/2, optimized_data['耗时(秒)'], width, label='优化版', color='orange')
    axes[1].set_title('计算时间比较', fontproperties=font_prop_title)
    axes[1].set_ylabel('时间 (秒)', fontproperties=font_prop_label)
    axes[1].set_xticks(x)
    axes[1].set_xticklabels(algorithms, fontproperties=font_prop_label)
    axes[1].legend(prop=font_prop_label)
    axes[1].grid(axis='y', alpha=0.3)

    # R²比较
    axes[2].bar(x - width/2, original_data['R²'], width, label='原版', color='lightblue')
    axes[2].bar(x + width/2, optimized_data['R²'], width, label='优化版', color='orange')
    axes[2].set_title('R²比较', fontproperties=font_prop_title)
    axes[2].set_ylabel('R²值', fontproperties=font_prop_label)
    axes[2].set_xticks(x)
    axes[2].set_xticklabels(algorithms, fontproperties=font_prop_label)
    axes[2].legend(prop=font_prop_label)
    axes[2].grid(axis='y', alpha=0.3)

    plt.tight_layout()
    plt.savefig('output/optimization_comparison.jpg', dpi=300, bbox_inches='tight')
    print("优化对比图已保存到 'output/optimization_comparison.jpg'")

# 保存最优权重和损失函数值
def save_optimal_weights_and_loss():
    """
    保存所有9种算法的最优权重、偏置项和损失函数值到CSV文件

    保存内容包括：
    - 算法名称
    - 最优损失值
    - 偏置项 b
    - 权重向量 w1, w2, ..., w256

    返回:
    DataFrame: 包含所有算法完整最优解的数据框
    """
    print("正在计算并保存9种算法的最优权重和损失函数值...")
    X_train, X_test, y_train, y_test = load_data()
    
    # 算法参数
    max_iterations = 1000
    tol = 1e-6
    
    # 用于保存结果的字典
    algorithm_data = {}
    
    # 1. 梯度下降法
    print("计算梯度下降法的最优权重...")
    w_gd, b_gd, losses_gd, _, _, _, _ = gd_fit(
        X_train, y_train, X_test, learning_rate=0.01, max_iterations=max_iterations, tol=tol
    )
    # 计算最终损失
    y_pred_gd = np.dot(X_train, w_gd) + b_gd
    loss_gd = np.mean((y_pred_gd - y_train) ** 2)
    algorithm_data["梯度下降法"] = {"最优损失": loss_gd, "权重": w_gd, "偏置项": b_gd}

    # 2. 牛顿法
    print("计算牛顿法的最优权重...")
    w_newton, b_newton, losses_newton, _, _, _, _ = newton_fit(
        X_train, y_train, X_test, max_iterations=max_iterations, tol=tol
    )
    # 计算最终损失
    y_pred_newton = np.dot(X_train, w_newton) + b_newton
    loss_newton = np.mean((y_pred_newton - y_train) ** 2)
    algorithm_data["牛顿法"] = {"最优损失": loss_newton, "权重": w_newton, "偏置项": b_newton}

    # 3. BFGS法
    print("计算BFGS法的最优权重...")
    w_bfgs, b_bfgs, losses_bfgs, _, _, _, _ = bfgs_fit(
        X_train, y_train, X_test, max_iterations=max_iterations, tol=tol
    )
    # 计算最终损失
    y_pred_bfgs = np.dot(X_train, w_bfgs) + b_bfgs
    loss_bfgs = np.mean((y_pred_bfgs - y_train) ** 2)
    algorithm_data["BFGS法"] = {"最优损失": loss_bfgs, "权重": w_bfgs, "偏置项": b_bfgs}

    # 4. 共轭梯度法
    print("计算共轭梯度法的最优权重...")
    w_cg, b_cg, losses_cg, _, _, _, _ = cg_fit(
        X_train, y_train, X_test, max_iterations=max_iterations, tol=tol
    )
    # 计算最终损失
    y_pred_cg = np.dot(X_train, w_cg) + b_cg
    loss_cg = np.mean((y_pred_cg - y_train) ** 2)
    algorithm_data["共轭梯度法"] = {"最优损失": loss_cg, "权重": w_cg, "偏置项": b_cg}

    # 5. Adagrad优化器
    print("计算Adagrad优化器的最优权重...")
    w_adam, b_adam, losses_adam, _, _, _, _ = adam_fit(
        X_train, y_train, X_test, learning_rate=0.1, max_iterations=max_iterations, tol=tol
    )
    # 计算最终损失
    y_pred_adam = np.dot(X_train, w_adam) + b_adam
    loss_adam = np.mean((y_pred_adam - y_train) ** 2)
    algorithm_data["Adagrad优化器"] = {"最优损失": loss_adam, "权重": w_adam, "偏置项": b_adam}

    # 6. L-BFGS法
    print("计算L-BFGS法的最优权重...")
    w_lbfgs, b_lbfgs, losses_lbfgs, _, _, _, _ = lbfgs_fit(
        X_train, y_train, X_test, max_iterations=max_iterations, tol=tol
    )
    # 计算最终损失
    y_pred_lbfgs = np.dot(X_train, w_lbfgs) + b_lbfgs
    loss_lbfgs = np.mean((y_pred_lbfgs - y_train) ** 2)
    algorithm_data["L-BFGS法"] = {"最优损失": loss_lbfgs, "权重": w_lbfgs, "偏置项": b_lbfgs}

    # 7. 优化版梯度下降法
    print("计算优化版梯度下降法的最优权重...")
    w_gd_opt, b_gd_opt, losses_gd_opt, _, _, _, _ = gd_optimized_fit(
        X_train, y_train, X_test, learning_rate=0.01, max_iterations=max_iterations, tol=tol
    )
    # 计算最终损失
    y_pred_gd_opt = np.dot(X_train, w_gd_opt) + b_gd_opt
    loss_gd_opt = np.mean((y_pred_gd_opt - y_train) ** 2)
    algorithm_data["梯度下降法(优化版)"] = {"最优损失": loss_gd_opt, "权重": w_gd_opt, "偏置项": b_gd_opt}

    # 8. 优化版牛顿法
    print("计算优化版牛顿法的最优权重...")
    w_newton_opt, b_newton_opt, losses_newton_opt, _, _, _, _ = newton_optimized_fit(
        X_train, y_train, X_test, max_iterations=max_iterations, tol=tol
    )
    # 计算最终损失
    y_pred_newton_opt = np.dot(X_train, w_newton_opt) + b_newton_opt
    loss_newton_opt = np.mean((y_pred_newton_opt - y_train) ** 2)
    algorithm_data["牛顿法(优化版)"] = {"最优损失": loss_newton_opt, "权重": w_newton_opt, "偏置项": b_newton_opt}

    # 9. 优化版BFGS法
    print("计算优化版BFGS法的最优权重...")
    w_bfgs_opt, b_bfgs_opt, losses_bfgs_opt, _, _, _, _ = bfgs_optimized_fit(
        X_train, y_train, X_test, max_iterations=max_iterations, tol=tol
    )
    # 计算最终损失
    y_pred_bfgs_opt = np.dot(X_train, w_bfgs_opt) + b_bfgs_opt
    loss_bfgs_opt = np.mean((y_pred_bfgs_opt - y_train) ** 2)
    algorithm_data["BFGS法(优化版)"] = {"最优损失": loss_bfgs_opt, "权重": w_bfgs_opt, "偏置项": b_bfgs_opt}
    
    # 创建新格式的CSV文件（每行一个算法）
    # 首先创建表头：算法名、最优损失、偏置项、权重w1到w256
    columns = ["算法", "最优损失", "偏置项b"] + [f"w{i+1}" for i in range(len(w_gd))]

    # 创建数据行
    rows = []
    for algo_name, data in algorithm_data.items():
        # 将算法名、损失值、偏置项和权重合并为一行
        row = [algo_name, data["最优损失"], data["偏置项"]] + data["权重"].tolist()
        rows.append(row)
    
    # 创建DataFrame
    df = pd.DataFrame(rows, columns=columns)
    
    # 保存为CSV
    df.to_csv('output/optimal_weights_and_loss_transposed.csv', index=False)
    print("最优权重、偏置项和损失已保存到 'output/optimal_weights_and_loss_transposed.csv'")
    
    return df

def main():
    """
    主函数，根据用户选择运行不同的实验
    """
    print("多元线性回归优化算法比较实验")
    print("="*40)
    print("1. 六种算法全面比较")
    print("2. 三种算法优化效果对比")
    print("3. 运行所有比较")
    print("4. 保存9种算法的最优权重、偏置项和损失函数值")
    print("="*40)
    
    try:
        choice = int(input("请选择运行模式 (1-4): "))
        
        if choice == 1:
            run_and_compare()
        elif choice == 2:
            run_optimization_comparison()
        elif choice == 3:
            run_and_compare()
            print("\n" + "="*40 + "\n")
            run_optimization_comparison()
        elif choice == 4:
            save_optimal_weights_and_loss()
        else:
            print("无效选择，请输入1-4之间的数字")
            
    except ValueError:
        print("无效输入，请输入数字")

if __name__ == "__main__":
    main()