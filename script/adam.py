import numpy as np
import time

def adam_optimizer(X, y, learning_rate=0.01, beta1=0.9, beta2=0.999, epsilon=1e-8, max_iterations=1000, tol=1e-6):
    """
    使用RMSprop算法求解多元线性回归（自适应学习率）

    参数:
    X: 特征矩阵，形状为 (n_samples, n_features)
    y: 目标向量，形状为 (n_samples,)
    learning_rate: 学习率
    beta1: 未使用（保持接口一致性）
    beta2: RMSprop衰减率
    epsilon: 防止除零的小常数
    max_iterations: 最大迭代次数
    tol: 收敛阈值

    返回:
    w: 权重向量
    b: 偏置
    losses: 每次迭代的损失值
    iterations: 迭代次数
    time_cost: 计算时间
    """
    # 记录开始时间
    start_time = time.time()

    # 样本数和特征数
    n_samples, n_features = X.shape

    # 初始化权重和偏置
    w = np.zeros(n_features)
    b = 0

    # 初始化RMSprop累积梯度平方
    v_w = np.zeros(n_features)
    v_b = 0

    # 记录每次迭代的损失
    losses = []

    # 迭代优化
    for i in range(max_iterations):
        # 计算当前预测值
        y_pred = np.dot(X, w) + b

        # 计算损失（均方误差）
        loss = np.mean((y_pred - y) ** 2)
        losses.append(loss)

        # 计算梯度
        dw = (2/n_samples) * np.dot(X.T, (y_pred - y))
        db = (2/n_samples) * np.sum(y_pred - y)

        # 更新累积梯度平方（Adagrad）
        v_w += dw ** 2
        v_b += db ** 2

        # 更新参数
        w -= learning_rate * dw / (np.sqrt(v_w) + epsilon)
        b -= learning_rate * db / (np.sqrt(v_b) + epsilon)

        # 检查是否收敛
        if i > 0 and abs(losses[i] - losses[i-1]) < tol:
            break

    # 计算耗时
    time_cost = time.time() - start_time

    return w, b, losses, i+1, time_cost

def fit_and_predict(X_train, y_train, X_test=None, learning_rate=0.001, beta1=0.9, beta2=0.999, epsilon=1e-8, max_iterations=1000, tol=1e-6):
    """
    使用Adam优化器拟合线性回归模型并预测
    
    参数:
    X_train: 训练特征矩阵
    y_train: 训练目标向量
    X_test: 测试特征矩阵 (如果为None则不进行预测)
    learning_rate: 学习率
    beta1: 一阶矩估计的指数衰减率
    beta2: 二阶矩估计的指数衰减率
    epsilon: 防止除零的小常数
    max_iterations: 最大迭代次数
    tol: 收敛阈值
    
    返回:
    w: 权重向量
    b: 偏置
    losses: 每次迭代的损失值
    iterations: 迭代次数
    time_cost: 计算时间
    r2_train: 训练集上的R^2值
    y_pred: 预测值 (如果X_test不为None)
    """
    # 调用Adam优化器
    w, b, losses, iterations, time_cost = adam_optimizer(
        X_train, y_train, learning_rate, beta1, beta2, epsilon, max_iterations, tol
    )
    
    # 计算训练集上的R^2
    y_train_pred = np.dot(X_train, w) + b
    ss_total = np.sum((y_train - np.mean(y_train)) ** 2)
    ss_residual = np.sum((y_train - y_train_pred) ** 2)
    r2_train = 1 - (ss_residual / ss_total)
    
    # 如果提供了测试集，计算预测值和R^2
    if X_test is not None:
        y_test_pred = np.dot(X_test, w) + b
        return w, b, losses, iterations, time_cost, r2_train, y_test_pred
    
    return w, b, losses, iterations, time_cost, r2_train
