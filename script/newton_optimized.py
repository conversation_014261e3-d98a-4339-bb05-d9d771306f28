import numpy as np
import time



def newton_method_optimized(X, y, max_iterations=100, tol=1e-6, use_analytical_solution=True):
    """
    优化版牛顿法求解多元线性回归

    优化特性:
    1. 对于线性回归，直接使用解析解 - 最快的方法
    2. 预计算矩阵乘法 - 减少重复计算
    3. 使用更高效的数值方法
    4. 智能初始化

    参数:
    X: 特征矩阵，形状为 (n_samples, n_features)
    y: 目标向量，形状为 (n_samples,)
    max_iterations: 最大迭代次数
    tol: 收敛阈值
    use_analytical_solution: 是否使用解析解

    返回:
    w: 权重向量
    b: 偏置
    losses: 每次迭代的损失值
    iterations: 迭代次数
    time_cost: 计算时间
    """
    # 记录开始时间
    start_time = time.time()

    # 样本数和特征数
    n_samples, n_features = X.shape

    # 为了简化计算，我们可以将偏置b看作是一个额外的特征
    X_b = np.c_[np.ones((n_samples, 1)), X]

    # 对于线性回归，可以直接使用解析解（正规方程）
    if use_analytical_solution:
        try:
            # 使用正规方程: theta = (X^T X)^(-1) X^T y
            XTX = np.dot(X_b.T, X_b)
            XTy = np.dot(X_b.T, y)

            # 添加小的正则化项以确保数值稳定性
            XTX_reg = XTX + 1e-8 * np.eye(n_features + 1)
            theta = np.linalg.solve(XTX_reg, XTy)

            # 计算最终损失
            y_pred = np.dot(X_b, theta)
            final_loss = np.mean((y_pred - y) ** 2)

            # 计算耗时
            time_cost = time.time() - start_time

            # 提取偏置和权重
            b = theta[0]
            w = theta[1:]

            return w, b, [final_loss], 1, time_cost

        except np.linalg.LinAlgError:
            # 如果解析解失败，回退到迭代方法
            pass

    # 迭代牛顿法（如果解析解失败或不使用解析解）
    theta = np.zeros(n_features + 1)
    losses = []

    # 预计算一些矩阵以提高效率
    XTX = np.dot(X_b.T, X_b)

    for i in range(max_iterations):
        # 计算当前预测值和损失
        y_pred = np.dot(X_b, theta)
        loss = np.mean((y_pred - y) ** 2)
        losses.append(loss)

        # 计算梯度
        grad = (2/n_samples) * np.dot(X_b.T, (y_pred - y))

        # 检查收敛性
        grad_norm = np.linalg.norm(grad)
        if grad_norm < tol:
            break

        # 海森矩阵（对于线性回归是常数）
        hessian = (2/n_samples) * XTX

        try:
            # 求解牛顿方向
            direction = np.linalg.solve(hessian, -grad)
            theta += direction

        except np.linalg.LinAlgError:
            # 如果求解失败，添加正则化
            hessian_reg = hessian + 1e-6 * np.eye(n_features + 1)
            direction = np.linalg.solve(hessian_reg, -grad)
            theta += direction

    # 计算耗时
    time_cost = time.time() - start_time

    # 提取偏置和权重
    b = theta[0]
    w = theta[1:]

    return w, b, losses, i+1, time_cost

def fit_and_predict(X_train, y_train, X_test=None, max_iterations=100, tol=1e-6, use_analytical_solution=True):
    """
    使用优化版牛顿法拟合线性回归模型并预测
    
    参数:
    X_train: 训练特征矩阵
    y_train: 训练目标向量
    X_test: 测试特征矩阵 (如果为None则不进行预测)
    max_iterations: 最大迭代次数
    tol: 收敛阈值
    regularization: 正则化参数
    
    返回:
    w: 权重向量
    b: 偏置
    losses: 每次迭代的损失值
    iterations: 迭代次数
    time_cost: 计算时间
    r2_train: 训练集上的R^2值
    y_pred: 预测值 (如果X_test不为None)
    """
    # 调用优化版牛顿法
    w, b, losses, iterations, time_cost = newton_method_optimized(
        X_train, y_train, max_iterations, tol, use_analytical_solution
    )
    
    # 计算训练集上的R^2
    y_train_pred = np.dot(X_train, w) + b
    ss_total = np.sum((y_train - np.mean(y_train)) ** 2)
    ss_residual = np.sum((y_train - y_train_pred) ** 2)
    r2_train = 1 - (ss_residual / ss_total)
    
    # 如果提供了测试集，计算预测值和R^2
    if X_test is not None:
        y_test_pred = np.dot(X_test, w) + b
        return w, b, losses, iterations, time_cost, r2_train, y_test_pred
    
    return w, b, losses, iterations, time_cost, r2_train
