import numpy as np
import time

def newton_method(X, y, max_iterations=100, tol=1e-6):
    """
    使用牛顿法求解多元线性回归
    
    参数:
    X: 特征矩阵，形状为 (n_samples, n_features)
    y: 目标向量，形状为 (n_samples,)
    max_iterations: 最大迭代次数
    tol: 收敛阈值
    
    返回:
    w: 权重向量
    b: 偏置
    losses: 每次迭代的损失值
    iterations: 迭代次数
    time_cost: 计算时间
    """
    # 记录开始时间
    start_time = time.time()
    
    # 样本数和特征数
    n_samples, n_features = X.shape
    
    # 为了简化计算，我们可以将偏置b看作是一个额外的特征
    # 在X矩阵中添加一列全为1的列，表示偏置项
    X_b = np.c_[np.ones((n_samples, 1)), X]
    
    # 初始化参数向量 theta (包含b和w)
    theta = np.zeros(n_features + 1)
    
    # 记录每次迭代的损失
    losses = []
    
    # 迭代优化
    for i in range(max_iterations):
        # 计算当前预测值
        y_pred = np.dot(X_b, theta)
        
        # 计算损失（均方误差）
        loss = np.mean((y_pred - y) ** 2)
        losses.append(loss)
        
        # 计算梯度 (一阶导数)
        grad = (2/n_samples) * np.dot(X_b.T, (y_pred - y))
        
        # 计算海森矩阵 (二阶导数)
        hessian = (2/n_samples) * np.dot(X_b.T, X_b)
        
        # 检查海森矩阵是否可逆
        try:
            # 计算海森矩阵的逆
            hessian_inv = np.linalg.inv(hessian)
            
            # 更新参数
            theta -= np.dot(hessian_inv, grad)
        except np.linalg.LinAlgError:
            # 如果海森矩阵奇异，添加一个小的正则化项
            hessian += 1e-5 * np.eye(n_features + 1)
            hessian_inv = np.linalg.inv(hessian)
            theta -= np.dot(hessian_inv, grad)
        
        # 检查是否收敛
        if i > 0 and abs(losses[i] - losses[i-1]) < tol:
            break
    
    # 计算耗时
    time_cost = time.time() - start_time
    
    # 提取偏置和权重
    b = theta[0]
    w = theta[1:]
    
    return w, b, losses, i+1, time_cost

def fit_and_predict(X_train, y_train, X_test=None, max_iterations=100, tol=1e-6):
    """
    使用牛顿法拟合线性回归模型并预测
    
    参数:
    X_train: 训练特征矩阵
    y_train: 训练目标向量
    X_test: 测试特征矩阵 (如果为None则不进行预测)
    max_iterations: 最大迭代次数
    tol: 收敛阈值
    
    返回:
    w: 权重向量
    b: 偏置
    losses: 每次迭代的损失值
    iterations: 迭代次数
    time_cost: 计算时间
    r2_train: 训练集上的R^2值
    y_pred: 预测值 (如果X_test不为None)
    """
    # 调用牛顿法
    w, b, losses, iterations, time_cost = newton_method(
        X_train, y_train, max_iterations, tol
    )
    
    # 计算训练集上的R^2
    y_train_pred = np.dot(X_train, w) + b
    ss_total = np.sum((y_train - np.mean(y_train)) ** 2)
    ss_residual = np.sum((y_train - y_train_pred) ** 2)
    r2_train = 1 - (ss_residual / ss_total)
    
    # 如果提供了测试集，计算预测值和R^2
    if X_test is not None:
        y_test_pred = np.dot(X_test, w) + b
        return w, b, losses, iterations, time_cost, r2_train, y_test_pred
    
    return w, b, losses, iterations, time_cost, r2_train 