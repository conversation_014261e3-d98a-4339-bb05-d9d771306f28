import numpy as np
import time

def gradient_descent_optimized(X, y, learning_rate=0.01, max_iterations=1000, tol=1e-6,
                              momentum=0.9, use_batch_optimization=True, adaptive_lr=True):
    """
    优化版梯度下降法求解多元线性回归

    优化特性:
    1. 批量优化 - 使用向量化操作提高效率
    2. 动量机制 - 加速收敛，减少震荡
    3. 自适应学习率 - 根据收敛情况调整学习率
    4. 改进的收敛判断 - 多重收敛条件

    参数:
    X: 特征矩阵，形状为 (n_samples, n_features)
    y: 目标向量，形状为 (n_samples,)
    learning_rate: 初始学习率
    max_iterations: 最大迭代次数
    tol: 收敛阈值
    momentum: 动量系数
    use_batch_optimization: 是否使用批量优化
    adaptive_lr: 是否使用自适应学习率

    返回:
    w: 权重向量
    b: 偏置
    losses: 每次迭代的损失值
    iterations: 迭代次数
    time_cost: 计算时间
    """
    # 记录开始时间
    start_time = time.time()

    # 样本数和特征数
    n_samples, n_features = X.shape

    if use_batch_optimization:
        # 批量优化：将偏置项合并到特征矩阵中
        X_b = np.c_[np.ones((n_samples, 1)), X]
        theta = np.zeros(n_features + 1)
        velocity = np.zeros(n_features + 1)

        # 记录每次迭代的损失
        losses = []
        current_lr = learning_rate

        for i in range(max_iterations):
            # 计算预测值和损失
            y_pred = np.dot(X_b, theta)
            loss = np.mean((y_pred - y) ** 2)
            losses.append(loss)

            # 计算梯度
            grad = (2/n_samples) * np.dot(X_b.T, (y_pred - y))

            # 自适应学习率
            if adaptive_lr and i > 0:
                loss_change = losses[i-1] - losses[i]
                if loss_change < 0:  # 损失增加
                    current_lr *= 0.9
                elif loss_change > 0.01 * abs(losses[i-1]):  # 损失减少很多
                    current_lr = min(current_lr * 1.01, learning_rate)

            # 动量更新
            velocity = momentum * velocity + current_lr * grad
            theta -= velocity

            # 收敛检查
            grad_norm = np.linalg.norm(grad)
            if i > 0:
                loss_change = abs(losses[i] - losses[i-1])
                relative_loss_change = loss_change / (abs(losses[i-1]) + 1e-10)

                if grad_norm < tol or relative_loss_change < tol:
                    break

        # 提取偏置和权重
        b = theta[0]
        w = theta[1:]

    else:
        # 传统方法
        w = np.zeros(n_features)
        b = 0
        velocity_w = np.zeros(n_features)
        velocity_b = 0
        losses = []
        current_lr = learning_rate

        for i in range(max_iterations):
            # 计算预测值和损失
            y_pred = np.dot(X, w) + b
            loss = np.mean((y_pred - y) ** 2)
            losses.append(loss)

            # 计算梯度
            dw = (2/n_samples) * np.dot(X.T, (y_pred - y))
            db = (2/n_samples) * np.sum(y_pred - y)

            # 自适应学习率
            if adaptive_lr and i > 0:
                loss_change = losses[i-1] - losses[i]
                if loss_change < 0:
                    current_lr *= 0.9
                elif loss_change > 0.01 * abs(losses[i-1]):
                    current_lr = min(current_lr * 1.01, learning_rate)

            # 动量更新
            velocity_w = momentum * velocity_w + current_lr * dw
            velocity_b = momentum * velocity_b + current_lr * db

            w -= velocity_w
            b -= velocity_b

            # 收敛检查
            grad_norm = np.sqrt(np.sum(dw**2) + db**2)
            if i > 0:
                loss_change = abs(losses[i] - losses[i-1])
                relative_loss_change = loss_change / (abs(losses[i-1]) + 1e-10)

                if grad_norm < tol or relative_loss_change < tol:
                    break

    # 计算耗时
    time_cost = time.time() - start_time

    return w, b, losses, i+1, time_cost

def fit_and_predict(X_train, y_train, X_test=None, learning_rate=0.01, max_iterations=1000,
                   tol=1e-6, momentum=0.9, use_batch_optimization=True, adaptive_lr=True):
    """
    使用优化版梯度下降法拟合线性回归模型并预测
    
    参数:
    X_train: 训练特征矩阵
    y_train: 训练目标向量
    X_test: 测试特征矩阵 (如果为None则不进行预测)
    learning_rate: 初始学习率
    max_iterations: 最大迭代次数
    tol: 收敛阈值
    momentum: 动量系数
    adaptive_lr: 是否使用自适应学习率
    lr_decay: 学习率衰减系数
    
    返回:
    w: 权重向量
    b: 偏置
    losses: 每次迭代的损失值
    iterations: 迭代次数
    time_cost: 计算时间
    r2_train: 训练集上的R^2值
    y_pred: 预测值 (如果X_test不为None)
    """
    # 调用优化版梯度下降法
    w, b, losses, iterations, time_cost = gradient_descent_optimized(
        X_train, y_train, learning_rate, max_iterations, tol,
        momentum, use_batch_optimization, adaptive_lr
    )
    
    # 计算训练集上的R^2
    y_train_pred = np.dot(X_train, w) + b
    ss_total = np.sum((y_train - np.mean(y_train)) ** 2)
    ss_residual = np.sum((y_train - y_train_pred) ** 2)
    r2_train = 1 - (ss_residual / ss_total)
    
    # 如果提供了测试集，计算预测值和R^2
    if X_test is not None:
        y_test_pred = np.dot(X_test, w) + b
        return w, b, losses, iterations, time_cost, r2_train, y_test_pred
    
    return w, b, losses, iterations, time_cost, r2_train
