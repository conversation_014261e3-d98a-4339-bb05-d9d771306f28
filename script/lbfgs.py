import numpy as np
import time
from collections import deque

def lbfgs_method(X, y, max_iterations=100, tol=1e-6, m=10):
    """
    使用L-BFGS（Limited-memory BFGS）求解多元线性回归
    
    参数:
    X: 特征矩阵，形状为 (n_samples, n_features)
    y: 目标向量，形状为 (n_samples,)
    max_iterations: 最大迭代次数
    tol: 收敛阈值
    m: 存储的历史信息数量
    
    返回:
    w: 权重向量
    b: 偏置
    losses: 每次迭代的损失值
    iterations: 迭代次数
    time_cost: 计算时间
    """
    # 记录开始时间
    start_time = time.time()
    
    # 样本数和特征数
    n_samples, n_features = X.shape
    
    # 为了简化计算，我们可以将偏置b看作是一个额外的特征
    # 在X矩阵中添加一列全为1的列，表示偏置项
    X_b = np.c_[np.ones((n_samples, 1)), X]
    
    # 初始化参数向量 theta (包含b和w)
    theta = np.zeros(n_features + 1)
    
    # 初始化L-BFGS历史信息存储
    s_history = deque(maxlen=m)  # 参数变化历史
    y_history = deque(maxlen=m)  # 梯度变化历史
    rho_history = deque(maxlen=m)  # 1/(y^T * s) 历史
    
    # 记录每次迭代的损失
    losses = []
    
    # 计算初始梯度
    y_pred = np.dot(X_b, theta)
    loss = np.mean((y_pred - y) ** 2)
    losses.append(loss)
    grad = (2/n_samples) * np.dot(X_b.T, (y_pred - y))
    
    # 迭代优化
    for i in range(max_iterations):
        # 计算搜索方向（使用L-BFGS两循环递归）
        q = grad.copy()
        
        # 第一个循环：从最新到最旧
        alpha_list = []
        for j in range(len(s_history)):
            idx = len(s_history) - 1 - j
            alpha = rho_history[idx] * np.dot(s_history[idx], q)
            alpha_list.append(alpha)
            q -= alpha * y_history[idx]
        
        # 初始Hessian逆矩阵近似（使用标量）
        if len(s_history) > 0:
            gamma = np.dot(s_history[-1], y_history[-1]) / np.dot(y_history[-1], y_history[-1])
        else:
            gamma = 1.0
        
        r = gamma * q
        
        # 第二个循环：从最旧到最新
        alpha_list.reverse()
        for j in range(len(s_history)):
            beta = rho_history[j] * np.dot(y_history[j], r)
            r += s_history[j] * (alpha_list[j] - beta)
        
        # 搜索方向
        p = -r
        
        # 执行线搜索以确定步长（简化版本）
        alpha = 1.0
        
        # 更新参数
        theta_new = theta + alpha * p
        
        # 计算新的梯度和损失
        y_pred_new = np.dot(X_b, theta_new)
        loss_new = np.mean((y_pred_new - y) ** 2)
        losses.append(loss_new)
        grad_new = (2/n_samples) * np.dot(X_b.T, (y_pred_new - y))
        
        # 计算s和y
        s = theta_new - theta
        y_grad = grad_new - grad
        
        # 更新L-BFGS历史信息
        if np.dot(y_grad, s) > 1e-10:  # 确保curvature condition满足
            s_history.append(s)
            y_history.append(y_grad)
            rho_history.append(1.0 / np.dot(y_grad, s))
        
        # 更新参数和梯度
        theta = theta_new
        grad = grad_new
        
        # 检查是否收敛
        if np.linalg.norm(grad) < tol:
            break
    
    # 计算耗时
    time_cost = time.time() - start_time
    
    # 提取偏置和权重
    b = theta[0]
    w = theta[1:]
    
    return w, b, losses, i+1, time_cost

def fit_and_predict(X_train, y_train, X_test=None, max_iterations=100, tol=1e-6, m=10):
    """
    使用L-BFGS拟合线性回归模型并预测
    
    参数:
    X_train: 训练特征矩阵
    y_train: 训练目标向量
    X_test: 测试特征矩阵 (如果为None则不进行预测)
    max_iterations: 最大迭代次数
    tol: 收敛阈值
    m: 存储的历史信息数量
    
    返回:
    w: 权重向量
    b: 偏置
    losses: 每次迭代的损失值
    iterations: 迭代次数
    time_cost: 计算时间
    r2_train: 训练集上的R^2值
    y_pred: 预测值 (如果X_test不为None)
    """
    # 调用L-BFGS方法
    w, b, losses, iterations, time_cost = lbfgs_method(
        X_train, y_train, max_iterations, tol, m
    )
    
    # 计算训练集上的R^2
    y_train_pred = np.dot(X_train, w) + b
    ss_total = np.sum((y_train - np.mean(y_train)) ** 2)
    ss_residual = np.sum((y_train - y_train_pred) ** 2)
    r2_train = 1 - (ss_residual / ss_total)
    
    # 如果提供了测试集，计算预测值和R^2
    if X_test is not None:
        y_test_pred = np.dot(X_test, w) + b
        return w, b, losses, iterations, time_cost, r2_train, y_test_pred
    
    return w, b, losses, iterations, time_cost, r2_train
