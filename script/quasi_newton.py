import numpy as np
import time

def bfgs_method(X, y, max_iterations=100, tol=1e-6):
    """
    使用BFGS（类牛顿法）求解多元线性回归
    
    参数:
    X: 特征矩阵，形状为 (n_samples, n_features)
    y: 目标向量，形状为 (n_samples,)
    max_iterations: 最大迭代次数
    tol: 收敛阈值
    
    返回:
    w: 权重向量
    b: 偏置
    losses: 每次迭代的损失值
    iterations: 迭代次数
    time_cost: 计算时间
    """
    # 记录开始时间
    start_time = time.time()
    
    # 样本数和特征数
    n_samples, n_features = X.shape
    
    # 为了简化计算，我们可以将偏置b看作是一个额外的特征
    # 在X矩阵中添加一列全为1的列，表示偏置项
    X_b = np.c_[np.ones((n_samples, 1)), X]
    
    # 初始化参数向量 theta (包含b和w)
    theta = np.zeros(n_features + 1)
    
    # 初始化BFGS逆海森矩阵的近似（开始时使用单位矩阵）
    H = np.eye(n_features + 1)
    
    # 记录每次迭代的损失
    losses = []
    
    # 计算初始梯度
    y_pred = np.dot(X_b, theta)
    loss = np.mean((y_pred - y) ** 2)
    losses.append(loss)
    grad = (2/n_samples) * np.dot(X_b.T, (y_pred - y))
    
    # 迭代优化
    for i in range(max_iterations):
        # 计算搜索方向
        p = -np.dot(H, grad)
        
        # 执行线搜索以确定步长（简化版本，可以使用更复杂的线搜索）
        alpha = 1.0
        
        # 更新参数
        theta_new = theta + alpha * p
        
        # 计算新的梯度
        y_pred_new = np.dot(X_b, theta_new)
        loss_new = np.mean((y_pred_new - y) ** 2)
        losses.append(loss_new)
        grad_new = (2/n_samples) * np.dot(X_b.T, (y_pred_new - y))
        
        # 计算差值
        s = alpha * p  # theta_new - theta
        y_grad = grad_new - grad
        
        # 更新BFGS逆海森矩阵近似
        try:
            # 计算 rho = 1 / (y_grad^T * s)
            rho = 1.0 / np.dot(y_grad, s)
            
            # 计算 BFGS 更新
            I = np.eye(n_features + 1)
            term1 = I - rho * np.outer(s, y_grad)
            term2 = I - rho * np.outer(y_grad, s)
            term3 = rho * np.outer(s, s)
            
            H = np.dot(np.dot(term1, H), term2) + term3
        except:
            # 如果遇到数值问题，重置H为单位矩阵
            H = np.eye(n_features + 1)
        
        # 更新参数和梯度
        theta = theta_new
        grad = grad_new
        
        # 检查是否收敛
        if i > 0 and abs(losses[i] - losses[i-1]) < tol:
            break
    
    # 计算耗时
    time_cost = time.time() - start_time
    
    # 提取偏置和权重
    b = theta[0]
    w = theta[1:]
    
    return w, b, losses, i+1, time_cost

def fit_and_predict(X_train, y_train, X_test=None, max_iterations=100, tol=1e-6):
    """
    使用BFGS（类牛顿法）拟合线性回归模型并预测
    
    参数:
    X_train: 训练特征矩阵
    y_train: 训练目标向量
    X_test: 测试特征矩阵 (如果为None则不进行预测)
    max_iterations: 最大迭代次数
    tol: 收敛阈值
    
    返回:
    w: 权重向量
    b: 偏置
    losses: 每次迭代的损失值
    iterations: 迭代次数
    time_cost: 计算时间
    r2_train: 训练集上的R^2值
    y_pred: 预测值 (如果X_test不为None)
    """
    # 调用BFGS方法
    w, b, losses, iterations, time_cost = bfgs_method(
        X_train, y_train, max_iterations, tol
    )
    
    # 计算训练集上的R^2
    y_train_pred = np.dot(X_train, w) + b
    ss_total = np.sum((y_train - np.mean(y_train)) ** 2)
    ss_residual = np.sum((y_train - y_train_pred) ** 2)
    r2_train = 1 - (ss_residual / ss_total)
    
    # 如果提供了测试集，计算预测值和R^2
    if X_test is not None:
        y_test_pred = np.dot(X_test, w) + b
        return w, b, losses, iterations, time_cost, r2_train, y_test_pred
    
    return w, b, losses, iterations, time_cost, r2_train 